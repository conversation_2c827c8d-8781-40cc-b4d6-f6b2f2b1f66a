/**
 * 通用配置文件
 * 存放两种语言共享的配置项
 */

// API基础路径配置
export const apiConfig = {
  // 开发环境配置
  development: {
    baseUrl: "/sparts-wb",
    imgUrl: "/fileviewer/_Files/",
    fileUrl: "/fileviewer/",
    timeout: 10000,
  },
  // 生产环境配置
  production: {
    baseUrl: "/sparts-wb", 
    imgUrl: "/fileviewer/_Files/",
    fileUrl: "/fileviewer/",
    timeout: 15000,
  }
}

// 应用通用配置
export const appConfig = {
  // 版本信息
  version: "1.0.0",
  
  // 主题配置
  theme: {
    primaryColor: "#ee0a24", // 红色主题
    secondaryColor: "#ff6b35",
    backgroundColor: "#f7f8fa",
    textColor: "#323233",
    borderColor: "#ebedf0",
  },
  
  // 布局配置
  layout: {
    // 移动端视口配置
    viewport: {
      width: 375,
      height: 667,
      unitPrecision: 5,
      minPixelValue: 1,
    },
    // 横竖屏配置
    orientation: {
      enableAutoRotate: true,
      defaultOrientation: "portrait", // portrait | landscape
      breakpoints: {
        mobile: 768,
        tablet: 1024,
        desktop: 1200,
      }
    }
  },
  
  // 功能开关配置
  features: {
    enablePWA: false,
    enableOffline: false,
    enablePushNotification: false,
    enableBiometric: false,
    enableDarkMode: false,
  },
  
  // 性能配置
  performance: {
    // 图片懒加载配置
    lazyload: {
      preload: 1.3,
      error: '/images/error.png',
      loading: '/images/loading.png',
    },
    // 分页配置
    pagination: {
      pageSize: 20,
      maxPageSize: 100,
    },
    // 缓存配置
    cache: {
      maxAge: 5 * 60 * 1000, // 5分钟
      maxSize: 50, // 最大缓存条目数
    }
  },
  
  // 文件上传配置
  upload: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'],
    maxFiles: 10,
  },
  
  // 地图配置（如果需要）
  map: {
    defaultZoom: 10,
    minZoom: 3,
    maxZoom: 18,
  },
  
  // 安全配置
  security: {
    tokenExpiry: 24 * 60 * 60 * 1000, // 24小时
    maxLoginAttempts: 5,
    lockoutDuration: 15 * 60 * 1000, // 15分钟
  },
  
  // 调试配置
  debug: {
    enableConsoleLog: import.meta.env.MODE === 'development',
    enablePerformanceMonitor: false,
    enableErrorReporting: import.meta.env.MODE === 'production',
  }
}

// 第三方服务配置
export const servicesConfig = {
  // 分析服务
  analytics: {
    enabled: import.meta.env.MODE === 'production',
    trackingId: '',
  },
  
  // 错误监控
  errorTracking: {
    enabled: import.meta.env.MODE === 'production',
    dsn: '',
  },
  
  // 推送服务
  push: {
    enabled: false,
    vapidKey: '',
  }
}

// 导出默认配置
export default {
  api: apiConfig,
  app: appConfig,
  services: servicesConfig,
}

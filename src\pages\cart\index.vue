<template>
  <div class="cart-page">
    <van-nav-bar :title="isFavor ? $t('cart.favorites') : $t('cart.title')" left-arrow @click-left="onClickLeft"
      fixed />

    <div class="cart-content">

      <!-- 商品列表 -->
      <div class="products-container">
        <!-- 加载状态 -->
        <div class="loading-container" v-if="loading">
          <van-loading color="#ee0a24" />
        </div>

        <!-- 空状态显示 -->
        <van-empty v-else-if="!commodityList.length"
          :description="isFavor ? $t('cart.favoritesEmpty') : $t('cart.empty')" image="search" />

        <!-- 商品列表 -->
        <template v-else>
          <div v-for="(group, groupIndex) in commodityList" :key="'group-' + groupIndex" class="product-group">
            <!-- 组标题 -->
            <div class="group-header">
              <div class="header-left">
                <van-checkbox v-model="group.checked" @change="(val) => handleCheckItemAllChange(val, group)"
                  class="group-checkbox" />
                <!-- 组图片 -->
                <div class="group-image" @click="previewGroupImages(group)">
                  <van-image :src="getImageUrl(group.productFileList || [])[0] || ''" fit="contain" radius="4">
                    <template #error>
                      <div class="image-placeholder">
                        <van-icon name="photo-o" size="20" />
                      </div>
                    </template>
                  </van-image>
                  <!-- 图片数量标识 -->
                  <div v-if="getImageUrl(group.productFileList || []).length > 1" class="image-count">
                    {{ getImageUrl(group.productFileList || []).length }}
                  </div>
                </div>
              </div>

              <div class="header-title">
                <span class="group-code">{{ group.materielCode }}</span>
                <span class="group-name">{{ group.materielNameEn }}</span>
              </div>
            </div>

            <div class="group-content">
              <!-- 组内商品列表 -->
              <div class="items-container">
                <div v-for="(item, itemIndex) in group.cartList || []" :key="'item-' + itemIndex" class="product-item">
                  <van-swipe-cell>
                    <div class="item-card">
                      <div class="item-top">
                        <van-checkbox v-model="item.checked" @change="() => handleCheckItemChange($event, group)"
                          class="item-checkbox" />
                        <div class="item-index">{{ getTableIndex(groupIndex, itemIndex) }}</div>
                        <div class="item-code">{{ item.materielCode }}</div>
                      </div>

                      <div class="item-middle">
                        <div class="item-name-row">
                          <div class="item-name" :class="{ 'clickable': item.bomId && item.filePath && item.indexNo }"
                            @click="item.bomId && item.filePath && item.indexNo ? jumpDetail(item) : null">
                            {{ item.materielNameEn }}
                            <van-icon v-if="item.bomId && item.filePath && item.indexNo" name="search" size="14"
                              class="bom-icon" />
                          </div>
                          <div class="item-price">{{ userInfo.currency === "1" ? "¥" :
                            userInfo.currency === "2" ? "$" :
                              userInfo.currency === "3" ? "€" : "" }}
                            {{ formatPrice(item.unitPrice) }}
                          </div>
                        </div>
                        <div class="item-info-row">
                          <div class="item-components">{{ $t('cart.component') }}: {{ item.fileName || $t('cart.none')
                            }}</div>
                        </div>
                      </div>

                      <div class="item-bottom">
                        <div class="item-buttons">
                          <!-- 收藏/购物车图标 -->
                          <template v-if="isFavor">
                            <van-button icon="cart-o" plain size="mini" class="action-button"
                              :class="{ active: item.existsFavorites === '1' }" @click="addCart(item)" />
                          </template>
                          <template v-else>
                            <van-button icon="star-o" plain size="mini" class="action-button"
                              :class="{ active: item.existsFavorites === '1' }" @click="addFavorite(item)" />
                          </template>

                          <!-- 适用于按钮 -->
                          <van-popover v-model:show="usedInVisible[item.materielCode]" trigger="click"
                            placement="bottom">
                            <template #reference>
                              <van-button size="mini" plain class="action-button"
                                @click.stop="usedInFunc(item.materielCode)">{{ $t('cart.usedIn') }}</van-button>
                            </template>
                            <div class="popover-content">
                              <div v-for="(usedItem, usedIdx) in currentUsedInData[item.materielCode] || []"
                                :key="'used-' + usedIdx" class="used-in-item">
                                <div class="used-name">{{ usedItem.materielNameEn }}</div>
                                <div class="used-code">{{ usedItem.materielCode }}</div>
                              </div>

                            </div>
                          </van-popover>
                        </div>

                        <!-- 购物车数量控制 -->
                        <div class="quantity-control" v-if="!isFavor">
                          <div class="quantity-section">
                            <span class="quantity-label">{{ $t('cart.quantity') }}:</span>
                            <van-stepper v-model="item.amount" min="1" button-size="24" input-width="40"
                              @change="amountChange(item)" class="quantity-stepper" />
                          </div>
                          <div class="subtotal">
                            <span class="subtotal-label">{{ $t('cart.total') }}</span>
                            <span class="subtotal-value">
                              {{ userInfo.currency === "1" ? "¥" :
                                userInfo.currency === "2" ? "$" :
                                  userInfo.currency === "3" ? "€" : "" }}
                              {{ formatPrice(item.amountOfMoney) }}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <template #right>
                      <van-button type="danger" square text="删除" class="delete-button" @click="deleteItem(item)" />
                    </template>
                  </van-swipe-cell>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-action-bar">
      <!-- 总金额显示区域 -->
      <div v-if="!isFavor && hasSelected" class="total-amount-bar">
        <div class="total-amount-info">
          <span class="total-label">{{ $t('cart.selectedItems') }}</span>
          <span class="total-count">{{ selectedItemCount }}{{ $t('cart.items') }}</span>
          <span class="total-label">{{ $t('cart.total') }}</span>
          <span class="total-price">
            {{ userInfo.currency === "1" ? "¥" :
              userInfo.currency === "2" ? "$" :
                userInfo.currency === "3" ? "€" : "" }}
            {{ formatPrice(totalSelectedPrice) }}
          </span>
        </div>
      </div>

      <!-- 操作区域 -->
      <div class="action-bar">
        <van-checkbox v-model="checkAll" :indeterminate="isIndeterminate" @change="handleCheckAllChange"
          class="select-all">
          {{ checkAllText }}
        </van-checkbox>
        <div class="action-buttons">
          <van-button icon="delete-o" size="small" @click="deleteSelected" :disabled="!hasSelected">{{ $t('cart.delete')
            }}</van-button>
          <van-button v-if="!isFavor" type="danger" icon="balance-o" size="small" @click="onCreate"
            :disabled="!hasSelected">{{ $t('cart.createOrder') }}</van-button>
        </div>
      </div>
    </div>

    <!-- BOM图表抽屉 -->
    <van-popup v-model:show="dialogBomCanvasVisible" position="bottom" :style="{ height: '100%' }"
      @close="handleBomCanvasClose" :lock-scroll="false">
      <div class="bom-drawer-container">
        <!-- 抽屉头部 -->
        <div class="bom-drawer-header">
          <div class="header-left">
            <span class="drawer-title">{{ t('cart.bomChart') }}</span>
          </div>
          <div class="header-right">
            <van-button icon="close" size="small" @click="handleBomCanvasClose" class="close-btn" />
          </div>
        </div>

        <!-- BOM Canvas组件 -->
        <div class="bom-canvas-wrapper">
          <BomCanvas :params="paramsBomCanvas" :target-part="targetPartInfo" v-if="dialogBomCanvasVisible" />
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { showNotify, showDialog, showImagePreview } from "vant";
import { useI18n } from "vue-i18n";
import { useUserStore } from "@/stores/user";
import { useRouteParamsStore } from "@/stores/useRouteParamsStore";
import { BASE_FILE_URL } from "@/utils/config";

// API 导入
import apiService from "@/utils/api";

// 组件导入
import BomCanvas from "@/components/BomCanvas.vue";

// 路由实例
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();
const rpStore = useRouteParamsStore();
const { t } = useI18n();

// 状态定义
const loading = ref(false);
const isFavor = ref(false);
const checkAll = ref(false);
const commodityList = ref([]);
const usedInVisible = ref({});
const currentUsedInData = ref({});
const userInfo = ref(userStore.userInfo || {});
const dialogBomCanvasVisible = ref(false);
const paramsBomCanvas = ref({
  id: "",
  existsCart: "",
  existsFavorites: "",
  filePath: "",
  bomId: "",
  materielCode: "",
  materielNameEn: "",
  materielName: "",
  indexNo: 0,
  quantity: 0,
  amount: 0,
  price: 0,
});
const targetPartInfo = ref(null);

// 计算属性 - 选中的商品数量
const selectedItemCount = computed(() => {
  let count = 0;
  commodityList.value.forEach(group => {
    if (group.cartList) {
      group.cartList.forEach(item => {
        if (item.checked) count++;
      });
    }
  });
  return count;
});

// 计算属性 - 选中商品总价格
const totalSelectedPrice = computed(() => {
  let total = 0;
  commodityList.value.forEach(group => {
    if (group.cartList) {
      group.cartList.forEach(item => {
        if (item.checked) {
          total += parseFloat(item.amountOfMoney || (item.unitPrice * (item.amount || 1)));
        }
      });
    }
  });
  return total;
});

// 计算属性 - 是否有商品被选中
const hasSelected = computed(() => {
  return commodityList.value.some((item) => {
    return item.cartList && item.cartList.some((subItem) => subItem.checked);
  });
});

// 计算属性 - 总商品数量
const totalItemCount = computed(() => {
  let count = 0;
  commodityList.value.forEach(group => {
    if (group.cartList) {
      count += group.cartList.length;
    }
  });
  return count;
});



// 计算属性 - 是否为半选状态
const isIndeterminate = computed(() => {
  const selectedCount = selectedItemCount.value;
  const totalCount = totalItemCount.value;
  return selectedCount > 0 && selectedCount < totalCount;
});

// 计算属性 - 全选按钮文本
const checkAllText = computed(() => {
  const selectedCount = selectedItemCount.value;
  const totalCount = totalItemCount.value;

  if (selectedCount === 0) {
    return t('cart.selectAll');
  } else if (selectedCount === totalCount) {
    return `${t('cart.selectAll')} (${totalCount})`;
  } else {
    return `${t('cart.selected')} ${selectedCount}/${totalCount}`;
  }
});

// 监听路由变化，确定是购物车还是收藏页
watch(
  () => route.query,
  (query) => {
    isFavor.value = query.type === "favor";
    getList();
  },
  { immediate: true, deep: true }
);

// 返回上一页
const onClickLeft = () => {
  router.back();
};

// 获取商品列表
function getList() {
  loading.value = true;
  if (isFavor.value) {
    apiService.cart
      .getCurrentUserFavorites()
      .then((res) => {
        commodityList.value = res || [];
      })
      .catch(() => {
        showNotify({ type: "danger", message: t('cart.loadingFavoritesFailed') });
      })
      .finally(() => {
        loading.value = false;
      });
  } else {
    apiService.cart
      .getCurrentUserCart()
      .then((res) => {
        commodityList.value = res || [];
      })
      .catch(() => {
        showNotify({ type: "danger", message: t('cart.loadingFailed') });
      })
      .finally(() => {
        loading.value = false;
      });
  }
}

// 全选/取消全选
const handleCheckAllChange = (val) => {
  commodityList.value.forEach((item) => {
    item.checked = val;
    if (item.cartList) {
      item.cartList.forEach((el) => {
        el.checked = val;
      });
    }
  });
};

// 勾选/取消勾选某个分组下的所有商品
const handleCheckItemAllChange = (e, list) => {
  if (list.cartList) {
    list.cartList.forEach((el) => {
      el.checked = e;
    });
  }
  // 更新全选状态
  const allItemsChecked = commodityList.value.every((group) => {
    return !group.cartList || group.cartList.every(item => item.checked);
  });
  checkAll.value = allItemsChecked;
};

// 勾选/取消勾选单个商品
const handleCheckItemChange = (_, commodityItem) => {
  if (!commodityItem.cartList) return;

  const uncheckedItems = commodityItem.cartList.filter((item) => !item.checked);
  commodityItem.checked = uncheckedItems.length === 0;

  // 更新全选状态
  const allItemsChecked = commodityList.value.every((group) => {
    return !group.cartList || group.cartList.every(item => item.checked);
  });
  checkAll.value = allItemsChecked;
};

// 获取商品在表格中的序号
const getTableIndex = (itemIndex, index) => {
  return (
    Array(itemIndex)
      .fill(null)
      .reduce(
        (total, _, currentIndex) =>
          total + (commodityList.value?.[currentIndex]?.cartList?.length || 0),
        0
      ) +
    index +
    1
  );
};

// 获取商品图片URL
const getImageUrl = (array, type) => {
  const data = array.reduce((total, current) => {
    const urlArray = current.split("/").reverse();
    const isProduct = type !== "all" ? urlArray[0].includes("product") : true;
    if (
      urlArray.length > 0 &&
      isProduct &&
      (urlArray[0].includes(".jpg") || urlArray[0].includes(".png"))
    ) {
      total.push(BASE_FILE_URL + current);
    }
    return total;
  }, []);
  return data;
};

// 格式化价格
const formatPrice = (price) => {
  if (!price) return "0.00";
  return parseFloat(price).toFixed(2);
};

// 预览组图片
const previewGroupImages = (group) => {
  const images = getImageUrl(group.productFileList || [], 'all'); // 添加'all'参数获取所有图片
  if (images.length > 0) {
    showImagePreview({
      images,
      startPosition: 0,
      closeable: true,
    });
  }
};

// 获取选中商品列表
const getCheckedList = () => {
  return commodityList.value.reduce((total, item) => {
    if (item.cartList) {
      const current = item.cartList.filter((el) => el.checked);
      return [...total, ...current];
    }
    return total;
  }, []);
};

// 删除单个商品
const deleteItem = (item) => {
  showDialog({
    title: t('common.confirm'),
    message: t('cart.deleteConfirm'),
    showCancelButton: true,
  }).then(() => {
    apiService.cart
      .deleteCartItem({
        id: item.id,
      })
      .then(() => {
        showNotify({ type: "success", message: t('cart.deleteSuccess') });
        getList();
      })
      .catch((error) => {
        showNotify({ type: "danger", message: t('cart.operationFailed') });
      });
  });
};

// 删除选中商品
const deleteSelected = () => {
  const selectedList = getCheckedList();
  if (selectedList.length) {
    showDialog({
      title: t('common.confirm'),
      message: `${t('cart.deleteConfirm')} ${selectedList.length} ${t('cart.items')}?`,
      showCancelButton: true,
    }).then(() => {
      apiService.cart
        .deleteCartItem({
          id: selectedList.map((item) => item.id).join(","),
        })
        .then(() => {
          showNotify({ type: "success", message: t('cart.deleteSuccess') });
          getList();
        })
        .catch((error) => {
          showNotify({ type: "danger", message: t('cart.operationFailed') });
        });
    });
  } else {
    showNotify({ type: "warning", message: t('cart.pleaseSelectItems') });
  }
};

// 添加到购物车
const addCart = (row) => {
  apiService.cart
    .addToCart([
      {
        amount: 1,
        bomItemsId: row.bomItemsId,
        productType: row.productType,
      },
    ])
    .then((res) => {
      if (res.code == "0") {
        // 添加购物车成功
        row.existsFavorites = "1";
      } else if (res.code == "1") {
        // 移除购物车成功
        row.existsFavorites = "0";
      } else {
        // 操作失败
        showNotify({ type: "warning", message: res.msg || t('cart.operationFailed') });
      }
    })
    .catch((error) => {
      showNotify({ type: "danger", message: t('cart.operationFailed') });
    });
};

// 添加到收藏夹（切换收藏状态）
const addFavorite = (row) => {
  apiService.cart
    .addToFavorites([
      {
        bomItemsId: row.bomItemsId,
        productType: row.productType,
      },
    ])
    .then((res) => {
      if (res.code == "0") {
        // 添加收藏成功
        row.existsFavorites = "1";
        // 移除收藏提示，不显示任何消息
      } else if (res.code == "1") {
        // 移除收藏成功
        row.existsFavorites = "0";
        // 移除收藏提示，不显示任何消息
      } else {
        // 操作失败
        showNotify({ type: "warning", message: res.msg || t('cart.operationFailed') });
      }
    })
    .catch((error) => {
      showNotify({ type: "danger", message: t('cart.operationFailed') });
    });
};

// 更新商品数量
const amountChange = (item) => {
  apiService.cart
    .updateCartItemCount([
      {
        id: item.id,
        amount: item.amount,
      },
    ])
    .then((res) => {
      if (res) {
        item.amountOfMoney = parseFloat(
          (parseFloat(item.unitPrice) * parseFloat(item.amount)).toFixed(2)
        );
      }
    })
    .catch((error) => {
      showNotify({ type: "danger", message: t('cart.updateQuantityFailed') });
    });
};

// 查看商品适用于哪些产品
const usedInFunc = (materielCode) => {
  usedInVisible.value[materielCode] = !usedInVisible.value[materielCode];

  if (
    usedInVisible.value[materielCode] &&
    (!currentUsedInData.value[materielCode] ||
      currentUsedInData.value[materielCode].length === 0)
  ) {
    apiService.catalog
      .getBomItemsUsedIn({ materielCode: materielCode })
      .then((data) => {
        currentUsedInData.value[materielCode] = data || [];
      })
      .catch((error) => {
        currentUsedInData.value[materielCode] = [];
      });
  }
};

// 查看BOM图表
const jumpDetail = (item) => {
  paramsBomCanvas.value.filePath = item.filePath;
  paramsBomCanvas.value.bomId = item.bomId;
  paramsBomCanvas.value.materielCode = item.materielCode;
  paramsBomCanvas.value.materielNameEn = item.materielNameEn;
  paramsBomCanvas.value.materielName = item.materielName;
  paramsBomCanvas.value.quantity = item.quantity;
  paramsBomCanvas.value.price = item.unitPrice;
  paramsBomCanvas.value.indexNo = item.indexNo;
  paramsBomCanvas.value.id = item.bomItemsId;

  if (isFavor.value) {
    paramsBomCanvas.value.existsFavorites = "1";
    paramsBomCanvas.value.existsCart = item.existsFavorites;
  } else {
    paramsBomCanvas.value.existsCart = "1";
    paramsBomCanvas.value.existsFavorites = item.existsFavorites;
  }

  // 设置目标零件信息用于自动定位和点击模拟
  targetPartInfo.value = {
    id: item.bomItemsId, // BomCanvas组件期望的id字段
    materielCode: item.materielCode,
    materielNameEn: item.materielNameEn,
    materielName: item.materielName,
    indexNo: item.indexNo,
    bomItemsId: item.bomItemsId // 保留原字段以备其他用途
  };
  dialogBomCanvasVisible.value = true;

};

// BOM图表关闭处理
const handleBomCanvasClose = () => {
  dialogBomCanvasVisible.value = false;
  targetPartInfo.value = null;
};



// 生成新订单
const onCreate = () => {
  const selectedList = commodityList.value.reduce((total, current) => {
    if (current.cartList) {
      const currentList = current.cartList
        .filter((el) => el.checked)
        .map((item) => ({
          amount: item.quantity || item.amount,
          ...item,
          shopId: item.id,
        }));
      if (currentList.length) {
        total.push({ ...current, cartList: currentList });
      }
    }
    return total;
  }, []);

  if (selectedList.length === 0) {
    showNotify({ type: "warning", message: t('cart.pleaseSelectItems') });
    return;
  }

  rpStore.setParam("selectedList", selectedList);
  router.push("/order/create");
};

// 初始化
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.cart-page {
  padding-top: 46px;
  padding-bottom: 200px;
  /* 增加底部间距以适应底部操作栏和底部导航栏，避免遮挡 */
  background-color: #f7f8fa;
  min-height: 100vh;
  /* 确保页面内容可以正常滚动，覆盖全局的overflow: hidden */
  overflow-y: auto;
  height: 100vh;
}

.cart-content {
  padding: 10px;
  padding-top: 0;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .select-all {
    font-size: 14px;
    font-weight: 500;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

/* 总金额栏 */
.total-amount-bar {
  margin-bottom: 10px;
  padding: 10px 12px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .total-amount-info {
    display: flex;
    align-items: center;
    justify-content: flex-end;

    .total-label {
      color: #646566;
      font-size: 13px;
      margin-right: 4px;
    }

    .total-count {
      color: #323233;
      font-size: 13px;
      margin-right: 12px;
    }

    .total-price {
      color: #ee0a24;
      font-size: 16px;
      font-weight: 500;
    }
  }
}

/* 商品容器 */
.products-container {
  margin-bottom: 10px;
}

/* 加载与空状态 */
.loading-container {
  display: flex;
  justify-content: center;
  padding: 40px 0;
}

/* 商品组 */
.product-group {
  margin-bottom: 12px;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

  .group-header {
    padding: 10px 12px;
    border-bottom: 1px solid #f5f5f5;
    background-color: #f7f8fa;
    display: flex;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;
      margin-right: 12px;

      .group-checkbox {
        margin-right: 10px;
      }

      .group-image {
        width: 40px;
        height: 40px;
        overflow: hidden;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        cursor: pointer;

        .van-image {
          width: 100%;
          height: 100%;
        }

        .image-placeholder {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          background-color: #f2f3f5;
          color: #c8c9cc;
        }

        .image-count {
          position: absolute;
          top: 1px;
          right: 1px;
          background: rgba(0, 0, 0, 0.6);
          color: white;
          font-size: 8px;
          padding: 1px 3px;
          border-radius: 6px;
          min-width: 12px;
          text-align: center;
          line-height: 1.2;
        }

        &:hover {
          opacity: 0.8;
        }
      }
    }

    .header-title {
      overflow: hidden;

      .group-code {
        display: block;
        font-size: 12px;
        color: #646566;
        margin-bottom: 2px;
      }

      .group-name {
        display: block;
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}

/* 商品列表容器 */
.items-container {
  width: 100%;
}

/* 商品项 */
.product-item {
  margin-bottom: 1px;

  &:last-child {
    margin-bottom: 0;
  }
}

/* 商品卡片 */
.item-card {
  background-color: #fff;
  padding: 12px;

  .item-top {
    display: flex;
    align-items: center;
    margin-bottom: 8px;

    .item-checkbox {
      margin-right: 8px;
    }

    .item-index {
      display: inline-block;
      background-color: #f2f3f5;
      color: #969799;
      font-size: 11px;
      padding: 1px 6px;
      border-radius: 10px;
      margin-right: 8px;
    }

    .item-code {
      font-size: 12px;
      color: #646566;
    }
  }

  .item-middle {
    display: flex;
    flex-direction: column;
    padding-left: 26px;
    margin-bottom: 10px;

    .item-name-row {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 6px;

      .item-name {
        font-size: 14px;
        font-weight: 500;
        color: #323233;
        flex: 1;
        margin-right: 12px;
        line-height: 1.3;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;

        &.clickable {
          color: #1989fa;
          cursor: pointer;
          display: flex;
          align-items: center;
          -webkit-line-clamp: unset;
          -webkit-box-orient: unset;

          &:active {
            opacity: 0.7;
          }
        }

        .bom-icon {
          margin-left: 4px;
          color: #1989fa;
          flex-shrink: 0;
        }
      }

      .item-price {
        flex-shrink: 0;
        color: #ee0a24;
        font-size: 15px;
        font-weight: 500;
      }
    }

    .item-info-row {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #646566;

      .item-quantity {
        margin-right: 10px;
      }

      .item-components {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .item-bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 26px;

    .item-buttons {
      display: flex;
      align-items: center;
      gap: 8px;

      .action-button {
        height: 24px;
        /* 统一按钮高度 - 调整为更矮 */
        min-width: 24px;
        /* 确保图标按钮有最小宽度 */
        display: flex;
        align-items: center;
        justify-content: center;

        &.active {
          color: #ee0a24;
          border-color: #ee0a24;
        }
      }
    }

    .quantity-control {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .quantity-section {
        display: flex;
        align-items: center;
        margin-bottom: 6px;

        .quantity-label {
          font-size: 12px;
          color: #646566;
          margin-right: 6px;
        }

        .quantity-stepper {
          :deep(.van-stepper__input) {
            font-weight: 500;
          }
        }
      }

      .subtotal {
        .subtotal-label {
          font-size: 12px;
          color: #969799;
          margin-right: 4px;
        }

        .subtotal-value {
          font-size: 14px;
          font-weight: 500;
          color: #ee0a24;
        }
      }
    }
  }
}

/* 删除按钮 */
.delete-button {
  height: 100%;
}

/* 弹出框 */
.popover-content {
  max-width: 180px;
  max-height: 180px;
  overflow-y: auto;
  padding: 2px 0;

  .used-in-item {
    padding: 6px 8px;

    .used-name {
      font-size: 12px;
      margin-bottom: 2px;
    }

    .used-code {
      font-size: 11px;
      opacity: 0.8;
    }
  }

  .no-data {
    padding: 10px;
    text-align: center;
    font-size: 12px;
  }
}

/* BOM图表容器 */
.bom-canvas-container {
  height: 60vh;
  overflow: auto;
}

/* 底部操作栏 */
.bottom-action-bar {
  position: fixed;
  bottom: 50px;
  /* 为底部导航栏留出空间 */
  left: 0;
  right: 0;
  background-color: #fff;
  border-top: 1px solid #ebedf0;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  /* 确保高于底部导航栏的z-index */
  /* 确保底部操作栏有足够的安全区域 */
  padding-bottom: env(safe-area-inset-bottom);

  .total-amount-bar {
    padding: 8px 16px;
    border-bottom: 1px solid #ebedf0;
    margin-bottom: 0;
    border-radius: 0;
    box-shadow: none;

    .total-amount-info {
      justify-content: flex-end;

      .total-label {
        font-size: 14px;
      }

      .total-count {
        font-size: 14px;
        font-weight: 500;
      }

      .total-price {
        font-size: 16px;
        font-weight: 600;
      }
    }
  }

  .action-bar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    /* 确保操作栏内容不会被底部安全区域遮挡 */
    padding-bottom: calc(12px + env(safe-area-inset-bottom));

    .select-all {
      font-size: 14px;
    }

    .action-buttons {
      display: flex;
      gap: 8px;
    }
  }
}

/* 横屏适配 */
@media screen and (orientation: landscape) {
  .cart-content {
    max-width: 900px;
    margin: 0 auto;
    padding: 10px 15px;
  }

  .item-bottom {
    flex-direction: row;
    align-items: center !important;

    .quantity-control {
      flex-direction: row !important;
      align-items: center !important;

      .quantity-section {
        margin-right: 15px;
        margin-bottom: 0 !important;
      }
    }
  }

  .bottom-action-bar {
    max-width: 900px;
    left: 50%;
    transform: translateX(-50%);

    .action-bar {
      padding: 8px 16px;
      padding-bottom: calc(8px + env(safe-area-inset-bottom));
    }

    .total-amount-bar {
      padding: 6px 16px;
    }
  }
}

/* 小屏幕优化 */
@media screen and (max-width: 375px) {
  .cart-page {
    padding-bottom: 220px;
    /* 小屏幕增加更多底部间距，避免遮挡 */
  }

  .bottom-action-bar {
    .action-bar {
      padding: 10px 12px;
      padding-bottom: calc(10px + env(safe-area-inset-bottom));

      .action-buttons {
        gap: 6px;

        .van-button {
          font-size: 12px;
          padding: 0 8px;
        }
      }
    }
  }
}

// BOM抽屉样式
.bom-drawer-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.bom-drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-left {
    .drawer-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .orientation-btn {
      margin-right: 4px;
    }

    .close-btn {
      color: #666;

      &:hover {
        color: #333;
      }
    }
  }
}

.bom-canvas-wrapper {
  flex: 1;
  overflow: hidden;
}

// 横屏模式下的抽屉适配
@media (orientation: landscape) {
  .bom-drawer-container {
    // 横屏时优化布局
    max-height: 100vh;

    // 确保内容区域充分利用空间
    .bom-drawer-content {
      flex: 1;
      min-height: 0; // 重要：允许flex子项收缩
    }
  }

  .bom-drawer-header {
    // 横屏时减少头部高度
    padding: 6px 16px;
    min-height: 44px;

    .header-left .drawer-title {
      font-size: 16px;
      font-weight: 600;
    }

    .header-right {
      .close-btn {
        padding: 4px 8px;
        min-width: 32px;
        height: 32px;
      }
    }
  }

  // 横屏时BOM Canvas区域优化
  .bom-drawer-content {
    // 移除多余的padding，最大化画布空间
    padding: 8px;

    // 确保BOM Canvas组件能充分利用空间
    :deep(.bom-canvas-container) {
      height: 100%;
      min-height: calc(100vh - 80px); // 减去头部高度
    }
  }
}
</style>

/**
 * 全局配置文件
 * 存放API基础路径和其他全局配置
 */

// API基础路径
export const BASE_URL = import.meta.env.VITE_API_BASE_URL || "/sparts-wb";

// 图片基础路径
export const BASE_IMG_URL = "/fileviewer/_Files/";

// 文件下载基础路径
export const BASE_FILE_URL = "/fileviewer/";

// 其他全局配置
export const config = {
  // 根据环境设置不同的配置
  development: {
    baseUrl: "/sparts-wb",
    imgUrl: "/fileviewer/_Files/",
    fileUrl: "/fileviewer/",
  },
  production: {
    baseUrl: "/sparts-wb",
    imgUrl: "/fileviewer/_Files/",
    fileUrl: "/fileviewer/",
  },
};

// 根据当前环境获取配置
export const currentConfig = config[import.meta.env.MODE || "development"];

<template>
  <div class="bom-canvas-container">
    <!-- 横屏模式：左右分栏布局 -->
    <div class="landscape-layout">
      <!-- 左侧控制面板 -->
      <div class="left-panel">
        <!-- 头部信息 -->
        <!-- <div class="bom-header">
          <div class="bom-info">
            <div class="model-info">
              <span>{{ $t('bom.model') }}: {{ params.materielCode }}</span>
            </div>
            <div class="service-info">
              <span>{{ params.materielNameEn }}</span>
            </div>
          </div>
        </div> -->

        <!-- 控制工具栏 -->
        <div class="controls-toolbar">
          <!-- 操作按钮组 -->
          <div class="controls-section">
            <div class="section-title">{{ $t('bom.operationControl') }}</div>
            <div class="controls-group">
              <van-button icon="minus" size="small" @click="zoomOut" :disabled="scale <= 0.5">
                {{ $t('bom.zoomOut') }}
              </van-button>
              <van-button icon="plus" size="small" @click="zoomIn" :disabled="scale >= 3">
                {{ $t('bom.zoomIn') }}
              </van-button>
              <van-button icon="replay" size="small" @click="resetView">
                {{ $t('bom.reset') }}
              </van-button>
            </div>
          </div>

          <!-- 查看列表按钮 -->
          <div class="controls-section">
            <div class="section-title">{{ $t('bom.viewOptions') }}</div>
            <div class="controls-group">
              <van-button icon="description" size="small" @click="toggleTable"
                :type="showTable ? 'primary' : 'default'">
                {{ showTable ? $t('bom.hideList') : $t('bom.showList') }}
              </van-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧画布区域 -->
      <div class="right-panel">
        <!-- Canvas容器 -->
        <div class="canvas-container" ref="canvasContainer" :style="{ height: canvasHeight }"
          @touchstart.passive="false" @touchmove.passive="false" @touchend.passive="false">

          <!-- Loading状态 -->
          <div v-if="isLoading" class="loading-overlay">
            <van-loading size="24px" vertical>
              <template #icon>
                <van-icon name="photo" size="24" />
              </template>
              {{ loadingText || $t('bom.loadingImage') }}
            </van-loading>
          </div>

          <!-- 统一Canvas -->
          <canvas ref="canvas" class="bom-canvas" @touchstart.prevent="handleTouchStart"
            @touchmove.prevent="handleTouchMove" @touchend.prevent="handleTouchEnd" />

          <!-- 当前选中物料信息 - 优化版本 -->
          <div v-if="currentItem" class="current-item-info" :style="currentItemPosition" @touchstart.stop
            @touchmove.stop @touchend.stop @click.stop>
            <div class="item-header">
              <span class="item-name">{{ currentItem.materielNameEn }}</span>
              <van-button icon="cross" size="mini" @click="hideCurrentItem" />
            </div>
            <div v-if="showItemDetails" class="item-details">
              <div class="item-row">
                <span>{{ $t('bom.partNumber') }}: {{ currentItem.materielCode }}</span>
              </div>
              <div class="item-row">
                <span>{{ $t('bom.quantity') }}: {{ currentItem.quantity }}</span>
              </div>
              <div class="item-row">
                <span>{{ $t('bom.price') }}: {{ formatPriceOptimized(currentItem.price) }}</span>
              </div>
              <div class="item-row">
                <span>{{ $t('bom.serialNumber') }}: {{ currentItem.indexNo }}</span>
              </div>
            </div>
            <div class="item-actions">
              <van-button icon="cart-o" size="mini" :type="currentItem.existsCart === '1' ? 'danger' : 'primary'"
                @click="toggleCartOptimized(currentItem)">
                {{ currentItem.existsCart === "1" ? $t('bom.removeFromCart') : $t('bom.addToCart') }}
              </van-button>
              <van-button icon="star-o" size="mini" :type="currentItem.existsFavorites === '1' ? 'warning' : 'default'"
                @click="toggleFavoriteOptimized(currentItem)">
                {{ $t('bom.addToFavorites') }}
              </van-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 零件列表 -->
    <van-popup v-model:show="showTable" position="bottom" :style="{ height: isLandscape ? '70%' : '60%' }" closeable
      close-icon="close">
      <div class="parts-table">
        <div class="table-header">
          <span>{{ $t('bom.partsList') }}</span>
        </div>
        <van-list>
          <div v-for="(item, index) in tableData" :key="index" class="part-item" @click="selectPart(item, index)"
            :class="{ active: currentItem?.indexNo === item.indexNo }">
            <div class="part-info">
              <div class="part-name">{{ item.materielNameEn }}</div>
              <div class="part-code">{{ item.materielCode }}</div>
              <div class="part-details">
                <span>{{ $t('bom.serialNumber') }}: {{ item.indexNo }}</span>
                <span>{{ $t('bom.quantity') }}: {{ item.quantity }}</span>
                <span>{{ $t('bom.price') }}: {{ formatPrice(item.price) }}</span>
              </div>
            </div>
            <div class="part-actions">
              <van-button icon="cart-o" size="mini" :type="item.existsCart === '1' ? 'danger' : 'primary'"
                @click.stop="toggleCart(item)" />
              <van-button icon="star-o" size="mini" :type="item.existsFavorites === '1' ? 'warning' : 'default'"
                @click.stop="toggleFavorite(item)" />
            </div>
          </div>
        </van-list>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from "vue";
import { showNotify } from "vant";
import { useI18n } from "vue-i18n";
import apiService from "@/utils/api";
import { BASE_FILE_URL } from "@/utils/config";

// Props
const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  targetPart: {
    type: Object,
    default: null,
  },
});

const { t } = useI18n();

// 响应式数据
const showTable = ref(false);
const showItemDetails = ref(true);
const isLoading = ref(false);
const loadingText = ref('');

const isLandscape = computed(() => {
  if (typeof window !== 'undefined') {
    return window.innerWidth > window.innerHeight;
  }
  return false;
});

const scale = ref(1);
const offsetX = ref(0);
const offsetY = ref(0);
const currentItem = ref(null);
const currentItemPosition = ref({ left: "0px", top: "0px" });
const tableData = ref([]);
const coordinates = ref([]);
const cartData = ref([]);

// Canvas相关
const canvasContainer = ref(null);
const canvas = ref(null);
const canvasImage = new Image();
let ctx = null;
let scaleFactor = 1;

// 触摸相关
const touchStart = ref(null);
const lastTouchDistance = ref(0);
const isDragging = ref(false);
const dragThreshold = 10;

const canvasHeight = computed(() => {
  const windowHeight = window.innerHeight;
  const windowWidth = window.innerWidth;
  const isLandscapeMode = windowWidth > windowHeight;

  if (isLandscapeMode) {
    const headerHeight = 50;
    const toolbarHeight = 40;
    const safeArea = 20;
    const availableHeight = windowHeight - headerHeight - toolbarHeight - safeArea;
    return `${Math.max(availableHeight, 300)}px`;
  } else {
    const headerHeight = 120;
    const availableHeight = windowHeight - headerHeight - 60;
    return `${Math.max(availableHeight, 400)}px`;
  }
});

const getTableData = async () => {
  try {
    const result = await apiService.catalog.getTableData({
      bomId: props.params.bomId,
    });
    tableData.value = result || [];
  } catch (error) {
    console.error("获取表格数据失败:", error);
  }
};

const getCoordinates = async () => {
  try {
    const result = await apiService.catalog.getCoordinate({
      bomId: props.params.bomId,
    });
    coordinates.value = result || [];
  } catch (error) {
    console.error("获取坐标数据失败:", error);
  }
};

const getCartData = async () => {
  try {
    cartData.value = [];
    const result = await apiService.cart.getCurrentUserCart();
    result.forEach((product) => {
      if (product.cartList) {
        product.cartList.forEach((cart) => {
          cartData.value.push(cart);
        });
      }
    });
  } catch (error) {
    console.error("获取购物车数据失败:", error);
  }
};

const initCanvas = () => {
  if (!canvas.value || !canvasContainer.value) {
    return false;
  }

  ctx = canvas.value.getContext("2d");
  const containerRect = canvasContainer.value.getBoundingClientRect();
  const dpr = window.devicePixelRatio || 1;

  canvas.value.width = containerRect.width * dpr;
  canvas.value.height = containerRect.height * dpr;
  canvas.value.style.width = containerRect.width + 'px';
  canvas.value.style.height = containerRect.height + 'px';

  ctx.scale(dpr, dpr);
  return true;
};

const convertPdfToPng = (pdfPath) => {
  const parts = pdfPath.split("/");
  const fileName = parts.pop().replace(".pdf", ".png");

  // 智能判断：如果路径中已经包含 'boms'，则不再添加
  if (!parts.includes("boms")) {
    parts.push("boms");
  }

  return parts.join("/") + "/" + fileName;
};

// 图片缓存
const imageCache = new Map();

const loadImage = () => {
  const imagePath = convertPdfToPng(props.params.filePath);
  const fullImageUrl = BASE_FILE_URL + imagePath;

  // 检查缓存
  if (imageCache.has(fullImageUrl)) {
    const cachedImage = imageCache.get(fullImageUrl);
    if (cachedImage.complete) {
      canvasImage.src = cachedImage.src;
      handleImageLoaded();
      return;
    }
  }

  isLoading.value = true;
  loadingText.value = t('bom.loadingImage');

  canvasImage.src = fullImageUrl;

  canvasImage.onload = () => {
    // 缓存图片
    imageCache.set(fullImageUrl, canvasImage);
    handleImageLoaded();
  };

  canvasImage.onerror = () => {
    isLoading.value = false;
    loadingText.value = '';
    showNotify({ type: "danger", message: t('bom.imageLoadFailed') });
  };
};

// 优化的图片加载完成处理
const handleImageLoaded = () => {
  isLoading.value = false;
  loadingText.value = '';

  const containerRect = canvasContainer.value.getBoundingClientRect();
  scaleFactor = containerRect.height / canvasImage.height;
  resetView();
  scheduleRenderOptimized('high');

  if (props.targetPart && tableData.value.length > 0 && coordinates.value.length > 0) {
    nextTick(() => {
      locateAndClickPart(props.targetPart);
    });
  }
};

const render = () => {
  if (!ctx || !canvasImage.complete) {
    return;
  }

  const containerRect = canvasContainer.value.getBoundingClientRect();
  if (!containerRect.width || !containerRect.height) {
    return;
  }

  ctx.clearRect(0, 0, containerRect.width, containerRect.height);

  const imageWidth = canvasImage.width * scaleFactor * scale.value;
  const imageHeight = canvasImage.height * scaleFactor * scale.value;

  const isImageVisible =
    offsetX.value + imageWidth > 0 &&
    offsetX.value < containerRect.width &&
    offsetY.value + imageHeight > 0 &&
    offsetY.value < containerRect.height;

  if (!isImageVisible) {
    const corrected = validateAndCorrectViewParams();
    if (!corrected) {
      resetView();
      return;
    }
  }

  try {
    ctx.drawImage(
      canvasImage,
      offsetX.value,
      offsetY.value,
      imageWidth,
      imageHeight
    );
  } catch (error) {
    return;
  }

  drawHotspots();
};

const drawHotspots = () => {
  if (!ctx || !coordinates.value.length) return;

  coordinates.value.forEach((coord) => {
    const x = coord.pointLx * scaleFactor * scale.value + offsetX.value;
    const y = coord.pointLy * scaleFactor * scale.value + offsetY.value;
    const width = (coord.pointRx - coord.pointLx) * scaleFactor * scale.value;
    const height = (coord.pointRy - coord.pointLy) * scaleFactor * scale.value;

    ctx.fillStyle = "rgba(0, 123, 255, 0.15)";
    ctx.fillRect(x, y, width, height);

    ctx.strokeStyle = "rgba(0, 123, 255, 0.4)";
    ctx.lineWidth = 1;
    ctx.strokeRect(x, y, width, height);

    const isInCart = tableData.value.some(
      (item) =>
        item.indexNo === coord.indexNo &&
        cartData.value.some((cart) => cart.bomItemsId === item.id)
    );

    if (isInCart) {
      ctx.fillStyle = "#ff6b35";
      const centerX = x + width * 0.1;
      const centerY = y + height * 0.1;
      const radius = Math.min(width, height) * 0.12;

      ctx.beginPath();
      ctx.arc(centerX, centerY, radius, 0, Math.PI * 2);
      ctx.fill();

      ctx.fillStyle = "white";
      ctx.font = `${radius}px Arial`;
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText("🛒", centerX, centerY);
    }

    if (currentItem.value && currentItem.value.indexNo === coord.indexNo) {
      ctx.fillStyle = "rgba(255, 59, 48, 0.25)";
      ctx.fillRect(x, y, width, height);

      ctx.strokeStyle = "#ff3b30";
      ctx.lineWidth = 2;
      ctx.strokeRect(x, y, width, height);
    }
  });
};

const formatPrice = (price) => {
  if (!price) return "0.00";
  return parseFloat(price).toFixed(2);
};

// 优化的价格格式化 - 使用缓存避免重复计算
const formatPriceOptimized = (() => {
  const cache = new Map();
  const MAX_CACHE_SIZE = 100;

  return (price) => {
    if (!price) return "0.00";

    const key = String(price);
    if (cache.has(key)) {
      return cache.get(key);
    }

    const formatted = parseFloat(price).toFixed(2);

    // 限制缓存大小
    if (cache.size >= MAX_CACHE_SIZE) {
      const firstKey = cache.keys().next().value;
      cache.delete(firstKey);
    }

    cache.set(key, formatted);
    return formatted;
  };
})();

const scheduleRender = (() => {
  let scheduled = false;
  return () => {
    if (!scheduled) {
      scheduled = true;
      requestAnimationFrame(() => {
        render();
        scheduled = false;
      });
    }
  };
})();

// 优化的渲染调度 - 支持防抖和优先级
const scheduleRenderOptimized = (() => {
  let scheduled = false;
  let highPriority = false;
  let debounceTimer = null;

  return (priority = 'normal') => {
    // 高优先级渲染立即执行
    if (priority === 'high') {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
        debounceTimer = null;
      }

      if (!scheduled || !highPriority) {
        scheduled = true;
        highPriority = true;
        requestAnimationFrame(() => {
          render();
          scheduled = false;
          highPriority = false;
        });
      }
      return;
    }

    // 普通优先级使用防抖
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }

    debounceTimer = setTimeout(() => {
      if (!scheduled) {
        scheduled = true;
        requestAnimationFrame(() => {
          render();
          scheduled = false;
        });
      }
      debounceTimer = null;
    }, 16); // 约60fps
  };
})();

const zoomIn = () => {
  if (scale.value < 3) {
    scale.value *= 1.2;
    updateCurrentItemPositionOptimized();
    scheduleRenderOptimized('high');
  }
};

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value /= 1.2;
    updateCurrentItemPositionOptimized();
    scheduleRenderOptimized('high');
  }
};

const resetView = () => {
  if (!canvasContainer.value || !canvasImage.complete) {
    return;
  }

  scale.value = 1;
  const containerRect = canvasContainer.value.getBoundingClientRect();
  const imageWidth = canvasImage.width * scaleFactor;
  const imageHeight = canvasImage.height * scaleFactor;

  offsetX.value = (containerRect.width - imageWidth) / 2;
  offsetY.value = (containerRect.height - imageHeight) / 2;

  updateCurrentItemPositionOptimized();
  scheduleRenderOptimized('high');
};

const handleTouchStart = (event) => {
  event.preventDefault();
  isDragging.value = false;

  if (event.touches.length === 1) {
    touchStart.value = {
      x: event.touches[0].clientX,
      y: event.touches[0].clientY,
      startX: event.touches[0].clientX,
      startY: event.touches[0].clientY,
      timestamp: Date.now()
    };
  } else if (event.touches.length === 2) {
    isDragging.value = true;
    const touch1 = event.touches[0];
    const touch2 = event.touches[1];
    lastTouchDistance.value = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  }
};

// 节流函数
const throttle = (func, limit) => {
  let inThrottle;
  return function() {
    const args = arguments;
    const context = this;
    if (!inThrottle) {
      func.apply(context, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  }
};

// 优化的触摸移动处理 - 使用节流提升性能
const handleTouchMoveOptimized = throttle((event) => {
  if (!touchStart.value) return;

  event.preventDefault();
  event.stopPropagation();

  if (event.touches.length === 1) {
    const currentX = event.touches[0].clientX;
    const currentY = event.touches[0].clientY;

    const totalDeltaX = currentX - touchStart.value.startX;
    const totalDeltaY = currentY - touchStart.value.startY;
    const totalDistance = Math.sqrt(totalDeltaX * totalDeltaX + totalDeltaY * totalDeltaY);

    if (totalDistance > dragThreshold) {
      isDragging.value = true;
    }

    if (isDragging.value) {
      const deltaX = currentX - touchStart.value.x;
      const deltaY = currentY - touchStart.value.y;

      offsetX.value += deltaX;
      offsetY.value += deltaY;

      updateCurrentItemPositionOptimized();
      scheduleRenderOptimized('high'); // 拖拽时使用高优先级渲染
    }

    touchStart.value.x = currentX;
    touchStart.value.y = currentY;

  } else if (event.touches.length === 2) {
    isDragging.value = true;

    const touch1 = event.touches[0];
    const touch2 = event.touches[1];
    const currentDistance = Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
      Math.pow(touch2.clientY - touch1.clientY, 2)
    );

    if (lastTouchDistance.value > 0) {
      const scaleChange = currentDistance / lastTouchDistance.value;
      const newScale = scale.value * scaleChange;

      if (newScale >= 0.5 && newScale <= 3) {
        scale.value = newScale;
        updateCurrentItemPositionOptimized();
        scheduleRenderOptimized('high'); // 缩放时使用高优先级渲染
      }
    }

    lastTouchDistance.value = currentDistance;
  }
}, 16); // 约60fps的节流

const handleTouchMove = (event) => {
  handleTouchMoveOptimized(event);
};

const handleTouchEnd = (event) => {
  if (event.touches.length === 0) {
    if (touchStart.value && !isDragging.value) {
      const touchDuration = Date.now() - touchStart.value.timestamp;

      if (touchDuration < 500) {
        const rect = canvas.value.getBoundingClientRect();
        const x = touchStart.value.startX - rect.left;
        const y = touchStart.value.startY - rect.top;

        handleCanvasClick(x, y);
      }
    }

    touchStart.value = null;
    lastTouchDistance.value = 0;
    isDragging.value = false;
  }
};

const handleCanvasClick = (x, y) => {
  const clickedCoord = coordinates.value.find((coord) => {
    const hotspotX = coord.pointLx * scaleFactor * scale.value + offsetX.value;
    const hotspotY = coord.pointLy * scaleFactor * scale.value + offsetY.value;
    const hotspotWidth =
      (coord.pointRx - coord.pointLx) * scaleFactor * scale.value;
    const hotspotHeight =
      (coord.pointRy - coord.pointLy) * scaleFactor * scale.value;

    return (
      x >= hotspotX &&
      x <= hotspotX + hotspotWidth &&
      y >= hotspotY &&
      y <= hotspotY + hotspotHeight
    );
  });

  if (clickedCoord) {
    const item = tableData.value.find(
      (item) => item.indexNo === clickedCoord.indexNo
    );
    if (item) {
      selectPart(item);
    }
  }
};

// 更新当前选中物料信息框的位置
const updateCurrentItemPosition = () => {
  if (!currentItem.value || !canvasContainer.value) return;

  const coord = coordinates.value.find((c) => c.indexNo === currentItem.value.indexNo);
  if (coord) {
    const containerRect = canvasContainer.value.getBoundingClientRect();

    const x = coord.pointLx * scaleFactor * scale.value + offsetX.value + 50;
    const y = coord.pointLy * scaleFactor * scale.value + offsetY.value + 50;

    currentItemPosition.value = {
      left: `${Math.max(10, Math.min(x, containerRect.width - 200))}px`,
      top: `${Math.max(10, Math.min(y, containerRect.height - 150))}px`,
    };
  }
};

// 优化的位置更新函数 - 使用防抖和缓存提升性能
const updateCurrentItemPositionOptimized = (() => {
  let cachedContainerRect = null;
  let lastUpdateTime = 0;
  const CACHE_DURATION = 100; // 缓存100ms

  return () => {
    if (!currentItem.value || !canvasContainer.value) return;

    const coord = coordinates.value.find((c) => c.indexNo === currentItem.value.indexNo);
    if (!coord) return;

    const now = Date.now();

    // 使用缓存的容器尺寸，减少DOM查询
    if (!cachedContainerRect || now - lastUpdateTime > CACHE_DURATION) {
      cachedContainerRect = canvasContainer.value.getBoundingClientRect();
      lastUpdateTime = now;
    }

    const x = coord.pointLx * scaleFactor * scale.value + offsetX.value + 50;
    const y = coord.pointLy * scaleFactor * scale.value + offsetY.value + 50;

    // 批量更新位置，避免多次DOM操作
    const newPosition = {
      left: `${Math.max(10, Math.min(x, cachedContainerRect.width - 200))}px`,
      top: `${Math.max(10, Math.min(y, cachedContainerRect.height - 150))}px`,
    };

    // 只在位置真正改变时才更新
    if (currentItemPosition.value.left !== newPosition.left ||
        currentItemPosition.value.top !== newPosition.top) {
      currentItemPosition.value = newPosition;
    }
  };
})();

const selectPart = (item) => {
  currentItem.value = item;

  const cartItem = cartData.value.find((cart) => cart.bomItemsId === item.id);
  if (cartItem) {
    item.existsCart = "1";
  }

  updateCurrentItemPosition();
  scheduleRender();
};

// 优化的零件选择函数 - 减少DOM操作和提升响应速度
const selectPartOptimized = (item) => {
  // 使用批量更新避免多次渲染
  const cartItem = cartData.value.find((cart) => cart.bomItemsId === item.id);
  if (cartItem) {
    item.existsCart = "1";
  }

  // 立即更新当前选中项
  currentItem.value = item;

  // 使用 nextTick 确保DOM更新后再计算位置
  nextTick(() => {
    updateCurrentItemPositionOptimized();
  });
};

// 优化的零件定位函数 - 提升性能和响应速度
const locateAndClickPart = (partInfo) => {
  if (!partInfo) {
    return false;
  }

  if (!tableData.value.length || !coordinates.value.length || !canvasImage.complete) {
    return false;
  }

  let targetItem = null;

  if (!targetItem && partInfo.id) {
    targetItem = tableData.value.find(item => item.id === partInfo.id);
  }

  if (!targetItem) {
    showNotify({
      type: 'warning',
      message: t('bom.partNotFound'),
      duration: 2000
    });
    return false;
  }

  const targetCoord = coordinates.value.find(coord => coord.indexNo === targetItem.indexNo);

  if (!targetCoord) {
    showNotify({
      type: 'warning',
      message: t('bom.partLocationNotFound'),
      duration: 2000
    });
    return false;
  }

  // 立即选中零件，避免延迟
  selectPartOptimized(targetItem);

  // 使用优化的视图调整，减少延迟
  adjustViewToShowPartOptimized(targetCoord.pointRx, targetCoord.pointRy, () => {
    // 动画完成后的回调
    validateAndCorrectViewParams();
    updateCurrentItemPositionOptimized();
    scheduleRenderOptimized('high');

    showNotify({
      type: 'success',
      message: t('bom.partLocated', { name: targetItem.materielNameEn || targetItem.materielCode }),
      duration: 2000
    });
  });

  return true;
};



// 优化的视图调整函数 - 支持回调和更快的动画
const adjustViewToShowPartOptimized = (partCenterX, partCenterY, callback) => {
  if (!canvasContainer.value || !canvasImage.complete) {
    callback && callback();
    return;
  }

  const containerRect = canvasContainer.value.getBoundingClientRect();
  const imageWidth = canvasImage.width * scaleFactor * scale.value;
  const imageHeight = canvasImage.height * scaleFactor * scale.value;

  const partScreenX = partCenterX * scaleFactor * scale.value + offsetX.value;
  const partScreenY = partCenterY * scaleFactor * scale.value + offsetY.value;

  const containerCenterX = containerRect.width / 2;
  const containerCenterY = containerRect.height / 2;

  const margin = 50;
  const isPartVisible =
    partScreenX >= margin && partScreenX <= containerRect.width - margin &&
    partScreenY >= margin && partScreenY <= containerRect.height - margin;

  if (!isPartVisible) {
    let targetOffsetX = containerCenterX - partCenterX * scaleFactor * scale.value;
    let targetOffsetY = containerCenterY - partCenterY * scaleFactor * scale.value;

    const minOffsetX = containerRect.width - imageWidth - 50;
    const maxOffsetX = 50;
    const minOffsetY = containerRect.height - imageHeight - 50;
    const maxOffsetY = 50;

    targetOffsetX = Math.max(minOffsetX, Math.min(maxOffsetX, targetOffsetX));
    targetOffsetY = Math.max(minOffsetY, Math.min(maxOffsetY, targetOffsetY));

    // 使用更快的动画时长和回调
    animateViewTransitionOptimized(offsetX.value, offsetY.value, targetOffsetX, targetOffsetY, callback);
  } else {
    scheduleRender();
    callback && callback();
  }
};

const validateAndCorrectViewParams = () => {
  if (!canvasContainer.value || !canvasImage.complete) return false;

  const containerRect = canvasContainer.value.getBoundingClientRect();
  const imageWidth = canvasImage.width * scaleFactor * scale.value;
  const imageHeight = canvasImage.height * scaleFactor * scale.value;

  const minOffsetX = containerRect.width - imageWidth - 50;
  const maxOffsetX = 50;
  const minOffsetY = containerRect.height - imageHeight - 50;
  const maxOffsetY = 50;

  let corrected = false;

  if (offsetX.value < minOffsetX) {
    offsetX.value = minOffsetX;
    corrected = true;
  } else if (offsetX.value > maxOffsetX) {
    offsetX.value = maxOffsetX;
    corrected = true;
  }

  if (offsetY.value < minOffsetY) {
    offsetY.value = minOffsetY;
    corrected = true;
  } else if (offsetY.value > maxOffsetY) {
    offsetY.value = maxOffsetY;
    corrected = true;
  }

  if (corrected) {
    scheduleRender();
  }

  return corrected;
};



// 优化的动画函数 - 更快的动画和回调支持
const animateViewTransitionOptimized = (startX, startY, endX, endY, callback, duration = 250) => {
  const startTime = Date.now();
  let animationId = null;

  const animate = () => {
    const elapsed = Date.now() - startTime;
    const progress = Math.min(elapsed / duration, 1);

    // 使用更平滑的缓动函数
    const easeProgress = progress < 0.5
      ? 4 * progress * progress * progress
      : 1 - Math.pow(-2 * progress + 2, 3) / 2;

    offsetX.value = startX + (endX - startX) * easeProgress;
    offsetY.value = startY + (endY - startY) * easeProgress;

    validateAndCorrectViewParams();
    scheduleRender();

    if (progress < 1) {
      animationId = requestAnimationFrame(animate);
    } else {
      // 动画完成，执行回调
      callback && callback();
    }
  };

  animationId = requestAnimationFrame(animate);

  // 返回取消函数，用于需要时中断动画
  return () => {
    if (animationId) {
      cancelAnimationFrame(animationId);
      animationId = null;
    }
  };
};

const hideCurrentItem = () => {
  currentItem.value = null;
  scheduleRender();
};

const toggleTable = () => {
  showTable.value = !showTable.value;
};

const toggleCart = async (item) => {
  try {
    const result = await apiService.cart.addToCart([
      {
        amount: 1,
        bomItemsId: item.bomItemsId || item.id,
        productType: item.productType || "part",
      },
    ]);

    if (result.code == "0") {
      item.existsCart = "1";
      showNotify({ type: "success", message: t('bom.addSuccess') });
    } else if (result.code == "1") {
      item.existsCart = "0";
      showNotify({ type: "success", message: t('bom.removeSuccess') });
    }

    await getCartData();
    scheduleRender();
  } catch (error) {
    showNotify({ type: "danger", message: t('bom.operationFailed') });
  }
};

const toggleFavorite = async (item) => {
  try {
    const result = await apiService.cart.addToFavorites([
      {
        bomItemsId: item.bomItemsId || item.id,
        productType: item.productType || "part",
      },
    ]);

    if (result.code == "0") {
      item.existsFavorites = "1";
    } else if (result.code == "1") {
      item.existsFavorites = "0";
    }

    scheduleRender();
  } catch (error) {
    showNotify({ type: "danger", message: t('bom.operationFailed') });
  }
};

// 优化的购物车操作 - 防抖和批量更新
const toggleCartOptimized = (() => {
  const pendingOperations = new Map();
  let debounceTimer = null;

  return async (item) => {
    const itemId = item.bomItemsId || item.id;

    // 防抖处理，避免重复点击
    if (pendingOperations.has(itemId)) {
      return;
    }

    pendingOperations.set(itemId, true);

    try {
      const result = await apiService.cart.addToCart([
        {
          amount: 1,
          bomItemsId: itemId,
          productType: item.productType || "part",
        },
      ]);

      if (result.code == "0") {
        item.existsCart = "1";
        showNotify({ type: "success", message: t('bom.addSuccess') });
      } else if (result.code == "1") {
        item.existsCart = "0";
        showNotify({ type: "success", message: t('bom.removeSuccess') });
      }

      // 延迟更新购物车数据，避免频繁请求
      if (debounceTimer) clearTimeout(debounceTimer);
      debounceTimer = setTimeout(async () => {
        await getCartData();
        scheduleRenderOptimized('normal');
      }, 500);

    } catch (error) {
      showNotify({ type: "danger", message: t('bom.operationFailed') });
    } finally {
      pendingOperations.delete(itemId);
    }
  };
})();

// 优化的收藏操作 - 防抖处理
const toggleFavoriteOptimized = (() => {
  const pendingOperations = new Map();

  return async (item) => {
    const itemId = item.bomItemsId || item.id;

    // 防抖处理，避免重复点击
    if (pendingOperations.has(itemId)) {
      return;
    }

    pendingOperations.set(itemId, true);

    try {
      const result = await apiService.cart.addToFavorites([
        {
          bomItemsId: itemId,
          productType: item.productType || "part",
        },
      ]);

      if (result.code == "0") {
        item.existsFavorites = "1";
      } else if (result.code == "1") {
        item.existsFavorites = "0";
      }

      scheduleRenderOptimized('normal');
    } catch (error) {
      showNotify({ type: "danger", message: t('bom.operationFailed') });
    } finally {
      pendingOperations.delete(itemId);
    }
  };
})();

const initBomCanvas = async () => {
  if (!props.params.filePath || !props.params.bomId) {
    showNotify({ type: "warning", message: t('bom.missingParams') });
    return;
  }

  try {
    isLoading.value = true;
    loadingText.value = t('bom.loadingData');

    await getTableData();
    await getCoordinates();
    await getCartData();

    loadingText.value = t('bom.initializingCanvas');

    await nextTick();
    initCanvas();
    loadImage();
  } catch (error) {
    isLoading.value = false;
    loadingText.value = '';
    showNotify({ type: "danger", message: t('bom.loadFailed') });
  }
};
watch(
  () => props.params,
  (newVal) => {
    if (newVal && Object.keys(newVal).length > 0) {
      initBomCanvas();
    }
  },
  { deep: true, immediate: true }
);

// 优化的targetPart监听器 - 使用防抖和立即响应
watch(
  () => props.targetPart,
  (() => {
    let debounceTimer = null;

    return (newTargetPart) => {
      if (debounceTimer) {
        clearTimeout(debounceTimer);
      }

      if (newTargetPart && tableData.value.length > 0 && coordinates.value.length > 0 && canvasImage.complete) {
        // 立即响应，无延迟
        nextTick(() => {
          locateAndClickPart(newTargetPart);
        });
      } else if (newTargetPart) {
        // 数据未准备好时，使用防抖等待
        debounceTimer = setTimeout(() => {
          if (tableData.value.length > 0 && coordinates.value.length > 0 && canvasImage.complete) {
            locateAndClickPart(newTargetPart);
          }
        }, 100);
      }
    };
  })(),
  { deep: true, immediate: false }
);

const handleResize = (() => {
  let timeoutId = null;
  let lastOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';

  return () => {
    if (timeoutId) clearTimeout(timeoutId);

    const currentOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
    const orientationChanged = currentOrientation !== lastOrientation;

    if (orientationChanged) {
      lastOrientation = currentOrientation;
    }

    const delay = orientationChanged ? 50 : 150;

    timeoutId = setTimeout(() => {
      nextTick(() => {
        if (initCanvas()) {
          if (orientationChanged) {
            scale.value = 1;
            offsetX.value = 0;
            offsetY.value = 0;
          }

          scheduleRender();

          showNotify({
            type: 'info',
            message: t('bom.adaptedToMode', {
              mode: currentOrientation === 'landscape' ? t('bom.landscapeMode') : t('bom.portraitMode')
            }),
            duration: 1500
          });
        }
      });
    }, delay);
  };
})();

const handleOrientationChange = () => {
  setTimeout(() => {
    handleResize();
  }, 100);
};

const preventPageGestures = (event) => {
  if (canvasContainer.value && canvasContainer.value.contains(event.target)) {
    const currentItemInfo = canvasContainer.value.querySelector('.current-item-info');
    if (currentItemInfo && currentItemInfo.contains(event.target)) {
      return;
    }

    if (event.target === canvas.value || event.target.closest('canvas')) {
      event.preventDefault();
      event.stopPropagation();
    }
  }
};

onMounted(() => {
  window.addEventListener("resize", handleResize);

  if (window.screen && window.screen.orientation) {
    window.screen.orientation.addEventListener('change', handleOrientationChange);
  }

  window.addEventListener('orientationchange', handleOrientationChange);

  document.addEventListener('touchstart', preventPageGestures, { passive: false });
  document.addEventListener('touchmove', preventPageGestures, { passive: false });
  document.addEventListener('touchend', preventPageGestures, { passive: false });
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  window.removeEventListener('orientationchange', handleOrientationChange);

  if (window.screen && window.screen.orientation) {
    window.screen.orientation.removeEventListener('change', handleOrientationChange);
  }

  document.removeEventListener('touchstart', preventPageGestures);
  document.removeEventListener('touchmove', preventPageGestures);
  document.removeEventListener('touchend', preventPageGestures);
});



defineExpose({
  locateAndClickPart,
  selectPart,
  resetView,
  zoomIn,
  zoomOut
});
</script>

<style lang="scss" scoped>
.bom-canvas-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  // 横屏模式下确保充满屏幕
  @media (orientation: landscape) {
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
  }
}

// 横屏模式的左右分栏布局
.landscape-layout {
  display: flex;
  flex-direction: column;
  height: 100%;

  // 在横屏模式下改为左右布局
  @media (orientation: landscape) {
    flex-direction: row;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
  }
}

// 左侧控制面板
.left-panel {
  // 竖屏模式下正常显示
  flex: none;

  // 横屏模式下固定宽度
  @media (orientation: landscape) {
    width: 280px;
    min-width: 280px;
    max-width: 320px;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    overflow-y: auto;
    background: #fff;
    border-right: 1px solid #eee;
    display: flex;
    flex-direction: column;
  }
}

// 右侧画布区域
.right-panel {
  // 竖屏模式下正常显示
  flex: 1;

  // 横屏模式下占据剩余空间
  @media (orientation: landscape) {
    flex: 1;
    height: 100vh;
    min-height: 100vh;
    max-height: 100vh;
    overflow: hidden;
    background: #f5f5f5;
    display: flex;
    flex-direction: column;
  }
}

.bom-header {
  padding: 6px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;

  .bom-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
  }

  .bom-info {
    font-size: 14px;
    color: #666;

    .model-info,
    .service-info {
      margin-bottom: 4px;
      line-height: 1.4;

      span {
        display: block;
      }
    }

    .model-info {
      font-weight: 600;
      color: #1976d2;
    }
  }

  // 横屏模式下的头部样式
  @media (orientation: landscape) {
    padding: 16px;
    border-bottom: 1px solid #eee;
    flex: none;

    .bom-info {

      .model-info,
      .service-info {
        margin-bottom: 8px;
        font-size: 13px;

        span {
          word-break: break-all;
        }
      }
    }
  }
}

.controls-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #eee;

  .controls-section {
    .section-title {
      display: none; // 竖屏模式下隐藏标题
    }

    .controls-group {
      display: flex;
      gap: 8px;
    }
  }

  // 横屏模式下的工具栏样式
  @media (orientation: landscape) {
    flex-direction: column;
    align-items: stretch;
    padding: 8px 16px;
    gap: 0px;
    overflow-y: auto;

    .controls-section {
      .section-title {
        display: block; // 横屏模式下显示标题
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
        padding-bottom: 6px;
        border-bottom: 1px solid #eee;
      }

      .controls-group {
        display: flex;
        flex-direction: row; // 横屏模式下按钮一行显示
        flex-wrap: wrap; // 允许换行

        .van-button {
          flex: 1;
          min-width: 0;
          height: 32px; // 降低按钮高度
          font-size: 12px;
          padding: 0 8px;

          // 确保按钮文字不会被截断
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      &:not(:last-child) {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
      }
    }

    .zoom-info {
      background: #f8f9fa;
      padding: 12px;
      border-radius: 6px;
      border: 1px solid #e9ecef;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .label {
          font-size: 13px;
          color: #666;
        }

        .value {
          font-size: 14px;
          font-weight: 600;
          color: #1976d2;
        }
      }
    }
  }
}

.canvas-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: #f0f0f0;

  // 防止浏览器手势冲突
  touch-action: none;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;

  .bom-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    touch-action: none;
    cursor: grab;

    // 防止图片拖拽和选择
    -webkit-user-drag: none;
    -khtml-user-drag: none;
    -moz-user-drag: none;
    -o-user-drag: none;

    // 防止长按菜单
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;

    &:active {
      cursor: grabbing;
    }
  }

  // 横屏模式下的画布容器样式
  @media (orientation: landscape) {
    height: 100vh !important;
    width: 100%;
    background: #f5f5f5;

    .bom-canvas {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
}

// Loading覆盖层样式
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  backdrop-filter: blur(2px);

  .van-loading {
    color: #1976d2;
    font-size: 14px;

    .van-icon {
      color: #1976d2;
      margin-bottom: 8px;
    }
  }

  // 横屏模式下的loading样式优化
  @media (orientation: landscape) {
    .van-loading {
      font-size: 13px;

      .van-icon {
        margin-bottom: 6px;
      }
    }
  }
}

.current-item-info {
  position: absolute;
  background: rgba(122, 114, 129, 0.95);
  color: white;
  border-radius: 8px;
  padding: 12px;
  min-width: 200px;
  max-width: 280px;
  z-index: 1000; // 提高层级，确保在最上层

  // 确保弹窗可以接收触摸事件
  pointer-events: auto;
  touch-action: auto;

  // 添加阴影效果，提升视觉层次
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);

  // 防止弹窗内容被选中
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;

  // 性能优化：启用硬件加速
  transform: translateZ(0);
  will-change: transform;

  // 优化动画性能
  transition: opacity 0.2s ease-out, transform 0.2s ease-out;

  // 防止重绘
  backface-visibility: hidden;

  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;

    .item-name {
      font-weight: 600;
      font-size: 14px;
      flex: 1;
      margin-right: 8px;
    }
  }

  .item-details {
    margin-bottom: 12px;

    .item-row {
      font-size: 12px;
      margin-bottom: 4px;
    }
  }

  .item-actions {
    display: flex;
    gap: 8px;

    .van-button {
      // 确保按钮可以正常点击
      pointer-events: auto;
      touch-action: manipulation;
    }
  }
}

.parts-table {
  height: 100%;
  display: flex;
  flex-direction: column;

  .table-header {
    padding: 16px;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #eee;
  }

  .part-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;

    &.active {
      background: #f0f8ff;
    }

    .part-info {
      flex: 1;

      .part-name {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .part-code {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
      }

      .part-details {
        font-size: 11px;
        color: #999;

        span {
          margin-right: 12px;
        }
      }
    }

    .part-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 横屏模式下的特殊样式调整
@media (orientation: landscape) {

  // 零件列表在横屏模式下的优化
  .parts-table {
    .table-header {
      padding: 8px 12px; /* 减少头部内边距 */
      font-size: 14px; /* 减小标题字体 */
      background: #f8f9fa;
    }

    .part-item {
      padding: 6px 12px; /* 减少列表项内边距 */

      .part-info {
        .part-name {
          font-size: 12px; /* 减小零件名称字体 */
          margin-bottom: 2px;
          line-height: 1.2;
        }

        .part-code {
          font-size: 10px; /* 减小零件代码字体 */
          margin-bottom: 2px;
        }

        .part-details {
          font-size: 9px; /* 减小详情字体 */

          span {
            margin-right: 8px; /* 减少间距 */
          }
        }
      }

      .part-actions {
        gap: 4px; /* 减少按钮间距 */

        .van-button {
          min-width: 28px; /* 减小按钮最小宽度 */
          height: 24px; /* 减小按钮高度 */
          padding: 0 4px; /* 减少按钮内边距 */
          font-size: 10px; /* 减小按钮字体 */
        }
      }
    }
  }

  // 当前选中物料信息在横屏模式下的优化
  .current-item-info {
    padding: 6px 8px; /* 减少内边距 */
    min-width: 160px; /* 减小最小宽度 */
    max-width: 200px; /* 减小最大宽度 */
    max-height: 220px; /* 减小最大高度 */
    overflow-y: auto;
    font-size: 11px; /* 减小字体 */

    .item-header .item-name {
      font-size: 12px; /* 减小标题字体 */
      line-height: 1.2;
    }

    .item-details .item-row {
      font-size: 10px; /* 减小详情字体 */
      line-height: 1.2;
      margin-bottom: 2px;
    }

    .item-actions {
      margin-top: 6px; /* 减少顶部间距 */

      .van-button {
        font-size: 11px;
        height: 28px;
        padding: 0 8px;
      }
    }
  }

  // 加载状态优化
  .loading-overlay {
    .van-loading {
      font-size: 14px;
    }
  }
}

// 大屏手机横屏模式下的进一步优化 (宽度 > 567px)
@media (orientation: landscape) and (min-width: 567px) {
  // 零件列表在大屏横屏下的进一步优化
  .parts-table {
    .table-header {
      padding: 6px 10px; /* 进一步减少头部内边距 */
      font-size: 13px; /* 进一步减小标题字体 */
    }

    .part-item {
      padding: 4px 10px; /* 进一步减少列表项内边距 */

      .part-info {
        .part-name {
          font-size: 11px; /* 进一步减小零件名称字体 */
          margin-bottom: 1px;
        }

        .part-code {
          font-size: 9px; /* 进一步减小零件代码字体 */
          margin-bottom: 1px;
        }

        .part-details {
          font-size: 8px; /* 进一步减小详情字体 */

          span {
            margin-right: 6px; /* 进一步减少间距 */
          }
        }
      }

      .part-actions {
        gap: 3px; /* 进一步减少按钮间距 */

        .van-button {
          min-width: 24px; /* 进一步减小按钮最小宽度 */
          height: 20px; /* 进一步减小按钮高度 */
          padding: 0 3px; /* 进一步减少按钮内边距 */
          font-size: 9px; /* 进一步减小按钮字体 */
        }
      }
    }
  }

  // 当前选中物料信息在大屏横屏下的进一步优化
  .current-item-info {
    padding: 4px 6px; /* 进一步减少内边距 */
    min-width: 140px; /* 进一步减小最小宽度 */
    max-width: 180px; /* 进一步减小最大宽度 */
    max-height: 200px; /* 进一步减小最大高度 */
    font-size: 10px; /* 进一步减小字体 */

    .item-header .item-name {
      font-size: 11px; /* 进一步减小标题字体 */
    }

    .item-details .item-row {
      font-size: 9px; /* 进一步减小详情字体 */
      margin-bottom: 1px;
    }

    .item-actions {
      margin-top: 4px; /* 进一步减少顶部间距 */

      .van-button {
        height: 20px; /* 进一步减小按钮高度 */
        font-size: 9px; /* 进一步减小按钮字体 */
        padding: 0 4px;
      }
    }
  }
}

// 平板设备下的零件列表优化 (宽度 768px - 1024px)
@media (min-width: 768px) and (max-width: 1024px) {
  .parts-table {
    .table-header {
      padding: 10px 16px; /* 平板设备下适中的内边距 */
      font-size: 15px; /* 平板设备下适中的字体 */
    }

    .part-item {
      padding: 8px 16px; /* 平板设备下适中的内边距 */

      .part-info {
        .part-name {
          font-size: 13px; /* 平板设备下适中的字体 */
          margin-bottom: 3px;
          line-height: 1.3;
        }

        .part-code {
          font-size: 11px; /* 平板设备下适中的字体 */
          margin-bottom: 3px;
        }

        .part-details {
          font-size: 10px; /* 平板设备下适中的字体 */

          span {
            margin-right: 12px; /* 平板设备下适中的间距 */
          }
        }
      }

      .part-actions {
        gap: 8px; /* 平板设备下适中的按钮间距 */

        .van-button {
          min-width: 36px; /* 平板设备下适中的按钮宽度 */
          height: 32px; /* 平板设备下适中的按钮高度 */
          padding: 0 8px; /* 平板设备下适中的按钮内边距 */
          font-size: 12px; /* 平板设备下适中的按钮字体 */
        }
      }
    }
  }

  // 当前选中物料信息在平板设备下的优化
  .current-item-info {
    padding: 12px 16px; /* 平板设备下增加内边距 */
    min-width: 220px; /* 平板设备下增加最小宽度 */
    max-width: 280px; /* 平板设备下增加最大宽度 */
    max-height: 300px; /* 平板设备下增加最大高度 */
    font-size: 13px; /* 平板设备下增加字体 */

    .item-header .item-name {
      font-size: 15px; /* 平板设备下增加标题字体 */
      line-height: 1.4;
    }

    .item-details .item-row {
      font-size: 12px; /* 平板设备下增加详情字体 */
      line-height: 1.4;
      margin-bottom: 4px;
    }

    .item-actions {
      margin-top: 12px; /* 平板设备下增加顶部间距 */

      .van-button {
        height: 36px; /* 平板设备下增加按钮高度 */
        font-size: 13px; /* 平板设备下增加按钮字体 */
        padding: 0 12px;
      }
    }
  }
}
</style>

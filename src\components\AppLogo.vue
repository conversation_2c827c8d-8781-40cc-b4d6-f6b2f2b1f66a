<template>
  <div class="app-logo" :class="{ 'app-logo--small': small }">
    <div class="logo-container">
      <img :src="logoSrc" alt="Logo" class="logo-image" />
    </div>
    <div class="logo-text" v-if="showTitle">
      <p class="app-name">{{ title }}</p>
      <p class="app-slogan" v-if="slogan">{{ slogan }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
// 导入logo图片
import logoImage from '@/assets/images/main/logo_turfone.png'

const props = defineProps({
  title: {
    type: String,
    default: 'ERPs Parts'
  },
  slogan: {
    type: String,
    default: '配件管理系统'
  },
  logoSrc: {
    type: String,
    default: logoImage
  },
  small: {
    type: Boolean,
    default: false
  },
  showTitle: {
    type: Boolean,
    default: true
  }
})
</script>

<style lang="scss" scoped>
.app-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  
  &--small {
    flex-direction: row;
    align-items: center;
    
    .logo-container {
      width: 36px;
      height: 36px;
      margin-right: 10px;
      margin-bottom: 0;
    }
    
    .logo-text {
      text-align: left;
      
      .app-name {
        font-size: 16px;
        margin-bottom: 0;
      }
      
      .app-slogan {
        font-size: 12px;
      }
    }
  }
  
  .logo-container {
    width: 70px;
    height: 70px;
    margin-bottom: 4px;
    
    .logo-image {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  
  .logo-text {
    text-align: center;
    
    .app-name {
      font-size: 22px;
      font-weight: bold;
      margin: 0 0 0;
      color: inherit;
    }
    
    .app-slogan {
      font-size: 14px;
      margin: 0;
      color: inherit;
      opacity: 0.8;
    }
  }
}
</style> 